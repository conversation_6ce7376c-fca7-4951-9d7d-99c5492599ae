import React, { useState } from "react";
import { Button, Table, Modal, Form, Input, Select, message } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";

const { TextArea } = Input;
const { Option } = Select;

const TreatmentProtocol = ({ userId }) => {
  const [protocols, setProtocols] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProtocol, setEditingProtocol] = useState(null);
  const [form] = Form.useForm();

  // Thêm phác đồ mới
  const handleAddProtocol = () => {
    setEditingProtocol(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // Xử lý submit form
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      const protocolData = {
        id: editingProtocol ? editingProtocol.id : Date.now(),
        ...values,
        createdAt: editingProtocol ? editingProtocol.createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      if (editingProtocol) {
        setProtocols(protocols.map(p => p.id === editingProtocol.id ? protocolData : p));
        message.success("Cập nhật phác đồ thành công!");
      } else {
        setProtocols([...protocols, protocolData]);
        message.success("Tạo phác đồ thành công!");
      }

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  const columns = [
    {
      title: "Tên phác đồ",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Chuyên khoa",
      dataIndex: "specialization",
      key: "specialization",
    },
    {
      title: "Mô tả",
      dataIndex: "description",
      key: "description",
      ellipsis: true,
    },
    {
      title: "Thao tác",
      key: "action",
      render: (_, record) => (
        <div>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingProtocol(record);
              form.setFieldsValue(record);
              setIsModalVisible(true);
            }}
          >
            Sửa
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddProtocol}
        >
          Tạo phác đồ mới
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={protocols}
        rowKey="id"
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title={editingProtocol ? "Sửa phác đồ" : "Tạo phác đồ mới"}
        open={isModalVisible}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        okText={editingProtocol ? "Cập nhật" : "Tạo"}
        cancelText="Hủy"
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="Tên phác đồ"
            rules={[{ required: true, message: "Vui lòng nhập tên phác đồ!" }]}
          >
            <Input placeholder="Nhập tên phác đồ" />
          </Form.Item>

          <Form.Item
            name="specialization"
            label="Chuyên khoa"
            rules={[{ required: true, message: "Vui lòng chọn chuyên khoa!" }]}
          >
            <Select placeholder="Chọn chuyên khoa">
              <Option value="Nội khoa">Nội khoa</Option>
              <Option value="Ngoại khoa">Ngoại khoa</Option>
              <Option value="Tim mạch">Tim mạch</Option>
              <Option value="Da liễu">Da liễu</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="Mô tả"
            rules={[{ required: true, message: "Vui lòng nhập mô tả!" }]}
          >
            <TextArea rows={4} placeholder="Nhập mô tả phác đồ điều trị" />
          </Form.Item>

          <Form.Item
            name="treatment"
            label="Phương pháp điều trị"
            rules={[{ required: true, message: "Vui lòng nhập phương pháp điều trị!" }]}
          >
            <TextArea rows={6} placeholder="Nhập chi tiết phương pháp điều trị" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TreatmentProtocol;