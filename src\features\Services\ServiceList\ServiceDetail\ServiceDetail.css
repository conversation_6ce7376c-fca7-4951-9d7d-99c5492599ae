.service-detail-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px 24px 32px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 10px;
  height: auto;
}

.service-info-detail {
  width: 50%;
}

/* <PERSON>ên tr<PERSON>i chứa thông tin dịch vụ */
.service-detail-left {
  flex: 1;
  max-width: calc(100% - 440px);
  /* giữ đủ chỗ cho bên phải */
  min-width: 0;
}

.service-detail-right {
  width: 400px;
  flex-shrink: 0;
}

/* <PERSON><PERSON>n ph<PERSON>i là form đặt lịch */
.service-detail-right {
  width: 400px;
  flex-shrink: 0;
}

.service-title {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #1e293b;
}

.service-price {
  color: #10b981;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
}

.service-description,
.service-preparation,
.service-detail-list {
  margin-bottom: 24px;
}

.service-details-wrapper {
  padding: 8px 0;
}

.service-description h3,
.prep-section h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #0f172a;
}

.service-description .description {
  margin-bottom: 24px;
  color: #334155;
  line-height: 1.6;
}

.prep-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.prep-item {
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.6;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

/* Responsive: Stack dọc trên mobile */
@media (max-width: 768px) {
  .service-detail-container {
    flex-direction: column;
    padding: 16px;
  }

  .service-detail-right {
    width: 100%;
    max-width: 100%;
  }
}

/* Tab items */

.ant-tabs-tab {
  padding: 12px 24px !important;
  font-size: 16px;
  font-weight: 600;
  color: #334155;
}

.ant-tabs-tab-active {
  background-color: #e0f7fa;
  border-radius: 8px;
  color: #0288d1 !important;
}

.ant-tabs-nav,
.ant-tabs-nav-wrap {
  justify-content: flex-start !important;
}

.ant-tabs-ink-bar {
  height: 4px;
  background-color: #0288d1 !important;
  border-radius: 2px;
}

/* Thêm CSS cho phần đánh giá */
.star-rating {
  display: inline-flex;
  margin-right: 8px;
}

.star {
  color: #e0e0e0;
  font-size: 24px;
}

.star.filled {
  color: #ffd700;
}

/* CSS cho danh sách đánh giá */
.feedback-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 16px 0;
}

.feedback-card {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.feedback-author {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.feedback-date {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 12px 0;
}

.feedback-comment {
  font-size: 15px;
  color: #374151;
  line-height: 1.6;
  margin: 0;
}

/* CSS cho danh sách bác sĩ */
.consultants-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 0;
}

.consultant-card {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.consultant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #2754d05a;
  transform: translateY(-1px);
}

.consultant-card:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.consultant-card .ant-card-body {
  padding: 16px;
}

.consultant-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.consultant-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.consultant-specialization {
  margin: 4px 0;
  color: #2753d0;
  font-weight: 500;
}

.consultant-email {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.consultant-details {
  flex: 1;
}

.consultant-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

/* CSS cho modal bác sĩ */
.consultant-modal-content {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.consultant-modal-left {
  flex-shrink: 0;
}

.consultant-modal-avatar {
  border: 3px solid #f0f0f0;
}

.consultant-modal-right {
  flex: 1;
}

.consultant-modal-name {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.consultant-modal-specialization {
  margin: 8px 0;
  color: #2753d0;
  font-weight: 500;
}

.consultant-modal-email,
.consultant-modal-phone,
.consultant-modal-experience,
.consultant-modal-description {
  margin: 8px 0;
  color: #374151;
  line-height: 1.5;
}

.consultant-modal-description {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

/* CSS cho empty states */
.empty-state {
  text-align: center;
  padding: 32px 0;
}

.empty-state-text {
  color: #888;
  margin-top: 12px;
}

/* CSS cho consultant card clickable */
.consultant-card-clickable {
  cursor: pointer;
}
